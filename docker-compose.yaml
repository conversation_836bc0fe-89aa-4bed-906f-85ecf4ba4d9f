# TempFly.io API - Eu VPS Configuration
#
# This configuration is optimized for Eu VPS with:
# - 2 shared vCPUs
# - 8GB RAM
# - 256GB SSD storage
#
# Resource allocation strategy:
# - API Service: 1 instance with 2 CPUs and 3GB RAM
# - PostgreSQL: 2 CPU and 3GB RAM
# - Redis: 1 CPU and 2GB RAM
# - Reserved for system: ~0.5GB RAM
#
# This configuration is designed to work with Coolify as the deployment platform.

#version: "3.9"

services:
  # Single API Service Instance (optimized for smaller server)
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 3g
    expose:
      - "3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=1
      # Database connection settings
      - PGHOST=postgres
      - PGPORT=5432
      - PGDATABASE=${PGDATABASE}
      - PGUSER=${PGUSER}
      - PGPASSWORD=${PGPASSWORD}
      - PGSSLMODE=disable
      # Reduced database pool for smaller server
      - PG_MAX_CONNECTIONS=10
      - PG_MIN_CONNECTIONS=2
      - PG_IDLE_TIMEOUT=20000
      # Redis connection (local)
      - REDIS_ENABLED=true
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=7200
      # Performance optimizations for smaller server
      - CONNECTION_WARMUP_INTERVAL=45000
      - MEMORY_CACHE_SIZE=200
      - MAX_MEMORY_CACHE_ITEMS=1000
      # API settings
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
        #is_directory: true
    depends_on:
      - postgres
      - redis
    networks:
      - tempmail-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 3g
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${PGUSER}
      - POSTGRES_PASSWORD=${PGPASSWORD}
      - POSTGRES_DB=${PGDATABASE}
      - POSTGRES_INITDB_ARGS=--auth-host=md5 --auth-local=trust
      - POSTGRES_HOST_AUTH_METHOD=md5
    command: >
      postgres
        -c shared_buffers=2GB
        -c effective_cache_size=2GB
        -c maintenance_work_mem=256MB
        -c work_mem=8MB
        -c max_connections=50
        -c max_worker_processes=8
        -c max_parallel_workers=2
        -c max_parallel_workers_per_gather=1
        -c random_page_cost=1.1
        -c effective_io_concurrency=100
        -c checkpoint_completion_target=0.9
        -c wal_buffers=8MB
        -c default_statistics_target=100
        -c autovacuum=on
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tempmail_user -d tempmail"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - tempmail-network

  # Redis Cache (smaller allocation)
  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 1g
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - tempmail-network

volumes:
  postgres_data:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
